"""
TaskExecutionAgent - The orchestrator for transparent task-based execution.

Implements Gemini's three principles:
1. Execution Rationale - plan() returns reasoning with task list
2. Agent Activity Instrumentation - Real-time activity updates
3. Post-Execution User Actions - Suggested next steps after completion

This transforms CodeQuilter from a "black box" AI tool into a transparent,
trustworthy development partner.
"""

from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime
import asyncio
import logging

from .task_models import Task, TaskStatus, ExecutionResult, SuggestedAction
from ..state.project_state import ProjectState, ProjectStatus
from ..integrations.real_llm_client import create_llm_client

logger = logging.getLogger(__name__)


class TaskExecutionAgent:
    """
    The core orchestrator for transparent task-based execution.
    
    Implements the Plan-Do-Verify loop with real-time user feedback.
    """
    
    def __init__(self, llm_client=None):
        """Initialize the TaskExecutionAgent"""
        self.llm_client = llm_client or create_llm_client(use_real_api=False)  # Use mock for v1
        self.activity_callback: Optional[Callable[[str, str], None]] = None  # (task_id, activity) -> None
        
    def set_activity_callback(self, callback: Callable[[str, str], None]) -> None:
        """Set callback for real-time activity updates"""
        self.activity_callback = callback
    
    async def plan(self, goal: str, context: Dict[str, Any]) -> Tuple[List[Task], str]:
        """
        Generate a task list with execution rationale.
        
        Implements Gemini Principle 1: Execution Rationale
        Returns both the task list and reasoning for the plan.
        
        Args:
            goal: High-level user goal (e.g., "add authentication to project")
            context: Current project context and state
            
        Returns:
            Tuple of (task_list, rationale_explanation)
        """
        logger.info(f"Planning tasks for goal: {goal}")
        
        # Analyze the goal and context to determine required tasks
        tasks = []
        rationale_parts = []
        
        # TODO: REPLACE_MOCK - Replace with real LLM-driven planning
        if "authentication" in goal.lower():
            tasks = self._plan_authentication_tasks(context)
            rationale_parts.append("To add authentication, I will first add the 'Passport.js' library")
            rationale_parts.append("then generate a standard adapter to handle JWT strategy")
            rationale_parts.append("and finally create the necessary API endpoints and integration tests")
            rationale_parts.append("This plan ensures a secure, testable implementation with minimal custom code")
            
        elif "component" in goal.lower() and "search" in goal.lower():
            tasks = self._plan_component_search_tasks(context)
            rationale_parts.append("To find components, I will analyze your project requirements")
            rationale_parts.append("search GitHub for matching repositories with health analysis")
            rationale_parts.append("and provide intelligent recommendations with integration guidance")
            rationale_parts.append("This approach ensures high-quality components that fit your architecture")
            
        elif "code" in goal.lower() and "generate" in goal.lower():
            tasks = self._plan_code_generation_tasks(context)
            rationale_parts.append("To generate code, I will analyze selected components")
            rationale_parts.append("create adapter code to integrate them seamlessly")
            rationale_parts.append("and generate project structure with proper configuration")
            rationale_parts.append("This ensures a working, professional-grade codebase")
            
        else:
            # Generic planning fallback
            tasks = self._plan_generic_tasks(goal, context)
            rationale_parts.append(f"To accomplish '{goal}', I will break this into manageable steps")
            rationale_parts.append("analyze the current project state and requirements")
            rationale_parts.append("execute each step with transparent progress updates")
            rationale_parts.append("This systematic approach ensures reliable completion")
        
        # Combine rationale parts into coherent explanation
        rationale = ". ".join(rationale_parts) + "."
        
        logger.info(f"Generated plan with {len(tasks)} tasks")
        return tasks, rationale
    
    def _plan_authentication_tasks(self, context: Dict[str, Any]) -> List[Task]:
        """Plan tasks for adding authentication"""
        return [
            Task(
                description="Search for authentication library (Passport.js)",
                metadata={"component_type": "authentication", "library": "passport"}
            ),
            Task(
                description="Analyze JWT strategy requirements",
                metadata={"strategy": "jwt", "security_level": "standard"}
            ),
            Task(
                description="Generate authentication adapter code",
                metadata={"adapter_type": "auth", "integration": "express"}
            ),
            Task(
                description="Create API endpoints for login/logout",
                metadata={"endpoints": ["POST /login", "POST /logout", "GET /profile"]}
            ),
            Task(
                description="Generate integration tests for authentication",
                metadata={"test_type": "integration", "coverage": "auth_flow"}
            )
        ]
    
    def _plan_component_search_tasks(self, context: Dict[str, Any]) -> List[Task]:
        """Plan tasks for component search and selection"""
        return [
            Task(
                description="Analyze project requirements from brainstorming",
                metadata={"source": "brainstorming_session", "patterns": context.get("patterns", [])}
            ),
            Task(
                description="Search GitHub for matching components",
                metadata={"search_type": "github_api", "filters": ["stars>1000", "recent_activity"]}
            ),
            Task(
                description="Perform health analysis on candidates",
                metadata={"analysis": ["license", "activity", "security", "popularity"]}
            ),
            Task(
                description="Generate intelligent recommendations",
                metadata={"llm_synthesis": True, "ranking": "health_score"}
            )
        ]
    
    def _plan_code_generation_tasks(self, context: Dict[str, Any]) -> List[Task]:
        """Plan tasks for code generation and assembly"""
        return [
            Task(
                description="Analyze selected component interfaces",
                metadata={"analysis_type": "api_surface", "tool": "tree_sitter"}
            ),
            Task(
                description="Generate adapter code between components",
                metadata={"generation_type": "adapter", "pattern": "integration"}
            ),
            Task(
                description="Create project structure and configuration",
                metadata={"structure": "standard", "config": ["docker", "env", "package"]}
            ),
            Task(
                description="Generate tests and documentation",
                metadata={"test_framework": "auto_detect", "docs": "readme"}
            )
        ]
    
    def _plan_generic_tasks(self, goal: str, context: Dict[str, Any]) -> List[Task]:
        """Fallback planning for generic goals"""
        return [
            Task(
                description=f"Analyze requirements for: {goal}",
                metadata={"goal": goal, "analysis_type": "requirements"}
            ),
            Task(
                description="Execute primary task steps",
                metadata={"goal": goal, "execution_type": "primary"}
            ),
            Task(
                description="Verify results and generate output",
                metadata={"goal": goal, "verification": True}
            )
        ]

    async def execute(self, task_list: List[Task], project_state: ProjectState) -> ExecutionResult:
        """
        Execute approved tasks with real-time updates.

        Implements Gemini Principle 2: Agent Activity Instrumentation
        Provides real-time activity updates for transparency.

        Args:
            task_list: List of approved tasks to execute
            project_state: Current project state to update

        Returns:
            ExecutionResult with completion status and suggested actions
        """
        logger.info(f"Starting execution of {len(task_list)} tasks")
        start_time = datetime.now()
        completed_tasks = []
        failed_task = None

        # Store task list in project state
        project_state.set_active_task_list(
            [task.to_dict() for task in task_list],
            {"execution_start": start_time.isoformat()}
        )

        try:
            # Execute tasks sequentially (v1 limitation)
            for task in task_list:
                logger.info(f"Executing task: {task.description}")

                # Start the task with initial activity
                task.start("Initializing task...")
                self._notify_activity_update(task.id, task.current_activity)

                # Update task in project state
                project_state.update_task_in_list(task.id, task.to_dict())

                # Execute the specific task
                success = await self._execute_single_task(task, project_state)

                if success:
                    task.complete("Task completed successfully")
                    completed_tasks.append(task)
                    logger.info(f"Task completed: {task.description}")
                else:
                    # Stop on first failure (v1 strategy)
                    failed_task = task
                    logger.error(f"Task failed: {task.description} - {task.error_message}")
                    break

                # Update task in project state
                project_state.update_task_in_list(task.id, task.to_dict())
                self._notify_activity_update(task.id, task.current_activity)

        except Exception as e:
            logger.error(f"Unexpected error during task execution: {e}")
            if task_list:
                current_task = task_list[len(completed_tasks)]
                current_task.fail(f"Unexpected error: {str(e)}")
                failed_task = current_task

        # Calculate total duration
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()

        # Create execution result with suggested actions
        result = ExecutionResult(
            success=failed_task is None,
            completed_tasks=completed_tasks,
            failed_task=failed_task,
            total_duration_seconds=total_duration
        )

        # Add appropriate suggested actions based on outcome
        if result.success:
            result.suggested_actions = result.get_success_actions()
            result.summary_message = f"Successfully completed all {len(completed_tasks)} tasks"
        else:
            # Determine if retry is appropriate (not for permanent failures)
            allow_retry = failed_task and "not found" not in (failed_task.error_message or "").lower()
            result.suggested_actions = result.get_failure_actions(allow_retry)
            result.summary_message = f"Execution stopped at task: {failed_task.description}"

        # Clear active task list from project state
        project_state.clear_active_task_list()

        logger.info(f"Execution completed. Success: {result.success}, Duration: {total_duration:.2f}s")
        return result

    def _notify_activity_update(self, task_id: str, activity: str) -> None:
        """Notify about task activity update via callback"""
        if self.activity_callback and activity:
            try:
                self.activity_callback(task_id, activity)
            except Exception as e:
                logger.warning(f"Activity callback failed: {e}")

    async def _execute_single_task(self, task: Task, project_state: ProjectState) -> bool:
        """
        Execute a single task with activity instrumentation.

        Returns True if successful, False if failed.
        Task object is updated with progress and results.
        """
        try:
            # Determine task type from metadata and description
            task_type = self._determine_task_type(task)

            # Execute based on task type
            if task_type == "component_search":
                return await self._execute_component_search_task(task, project_state)
            elif task_type == "code_generation":
                return await self._execute_code_generation_task(task, project_state)
            elif task_type == "analysis":
                return await self._execute_analysis_task(task, project_state)
            elif task_type == "authentication":
                return await self._execute_authentication_task(task, project_state)
            else:
                return await self._execute_generic_task(task, project_state)

        except Exception as e:
            task.fail(f"Task execution error: {str(e)}")
            return False

    def _determine_task_type(self, task: Task) -> str:
        """Determine the type of task based on description and metadata"""
        description = task.description.lower()
        metadata = task.metadata or {}

        if "search" in description and "component" in description:
            return "component_search"
        elif "generate" in description and "code" in description:
            return "code_generation"
        elif "analyze" in description or "analysis" in description:
            return "analysis"
        elif "authentication" in description or metadata.get("component_type") == "authentication":
            return "authentication"
        else:
            return "generic"

    async def _execute_component_search_task(self, task: Task, project_state: ProjectState) -> bool:
        """Execute component search related task"""
        # TODO: REPLACE_MOCK - Integrate with real ComponentMatcher
        task.update_activity("Searching GitHub repositories...")
        await asyncio.sleep(0.5)  # Simulate work

        task.update_activity("Analyzing component health...")
        await asyncio.sleep(0.5)

        task.update_activity("Generating recommendations...")
        await asyncio.sleep(0.3)

        # Mock successful completion
        return True

    async def _execute_code_generation_task(self, task: Task, project_state: ProjectState) -> bool:
        """Execute code generation related task"""
        # TODO: REPLACE_MOCK - Integrate with real code generation modules
        task.update_activity("Analyzing component interfaces...")
        await asyncio.sleep(0.7)

        task.update_activity("Generating adapter code...")
        await asyncio.sleep(1.0)

        task.update_activity("Formatting and validating code...")
        await asyncio.sleep(0.4)

        return True

    async def _execute_analysis_task(self, task: Task, project_state: ProjectState) -> bool:
        """Execute analysis related task"""
        task.update_activity("Loading project requirements...")
        await asyncio.sleep(0.3)

        task.update_activity("Performing analysis...")
        await asyncio.sleep(0.8)

        task.update_activity("Compiling results...")
        await asyncio.sleep(0.2)

        return True

    async def _execute_authentication_task(self, task: Task, project_state: ProjectState) -> bool:
        """Execute authentication related task"""
        task.update_activity("Configuring authentication strategy...")
        await asyncio.sleep(0.6)

        task.update_activity("Setting up security middleware...")
        await asyncio.sleep(0.8)

        task.update_activity("Generating auth endpoints...")
        await asyncio.sleep(0.5)

        return True

    async def _execute_generic_task(self, task: Task, project_state: ProjectState) -> bool:
        """Execute generic task with basic progress simulation"""
        task.update_activity("Processing task requirements...")
        await asyncio.sleep(0.4)

        task.update_activity("Executing task logic...")
        await asyncio.sleep(0.6)

        task.update_activity("Finalizing results...")
        await asyncio.sleep(0.3)

        return True
